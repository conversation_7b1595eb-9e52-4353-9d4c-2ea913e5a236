# 贵州民族大学人文科技学院 RAG 聊天应用

一个基于 LlamaIndex 和 Qdrant 的智能 RAG（检索增强生成）聊天应用，专为贵州民族大学人文科技学院设计，支持 ChestnutCMS 文章同步和智能问答。

## 🌟 功能特点

- 🔍 **混合检索**: 结合 BM25 稀疏向量和密集向量的混合检索
- 💬 **智能对话**: 基于 GPT-4o-mini 的自然语言问答
- 📄 **文档管理**: 支持 TXT 文件上传和 ChestnutCMS 文章同步
- 🌐 **Web 界面**: 响应式聊天界面和文档管理界面
- ⚡ **快速部署**: 一键启动，自动配置
- 🗄️ **Qdrant 存储**: 高性能向量数据库存储
- 🔄 **文件替换**: 支持同名文件完全替换机制
- 📱 **响应式设计**: 支持桌面和移动设备
- 🗃️ **CMS 集成**: 自动同步 ChestnutCMS 文章到 RAG 系统
- 🔗 **引用链接**: 回答中包含可点击的原文链接
- 🌍 **CORS 支持**: 支持跨域访问，可嵌入外部网站

## 🚀 快速开始

### 1. 环境要求

- Python 3.11+
- OpenAI API 密钥（支持代理）
- Qdrant 向量数据库（本地或远程）
- MySQL 数据库（用于 ChestnutCMS 集成，可选）

### 2. 安装和配置

```bash
# 克隆项目
git clone https://github.com/JamesFein/fast-gzmdrw-chat.git
cd fast-gzmdrw-chat

# 创建虚拟环境（推荐）
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 创建环境配置文件
cp .env.example .env
# 编辑 .env 文件，填入你的配置
```

### 3. 配置 .env 文件

创建 `.env` 文件并配置以下参数：

```env
# OpenAI API配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://your-proxy-url.com/v1
OPENAI_MODEL=gpt-4o-mini
EMBEDDING_MODEL=text-embedding-3-small

# 应用配置
APP_HOST=0.0.0.0
APP_PORT=8000
DATA_DIR=./data
COLLECTION_NAME=documents
DEBUG=false
ENVIRONMENT=production

# Qdrant 向量数据库配置
QDRANT_HOST=localhost
QDRANT_PORT=6333
QDRANT_GRPC_PORT=6334
QDRANT_API_KEY=
QDRANT_PREFER_GRPC=true

# 混合检索配置
DEFAULT_BM25_WEIGHT=0.5
DEFAULT_VECTOR_WEIGHT=0.5
DEFAULT_SIMILARITY_TOP_K=5
DEFAULT_RETRIEVAL_MODE=hybrid

# CORS配置
ALLOWED_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000", "http://localhost:8000", "http://127.0.0.1:8000", "file://", "*"]

# MySQL数据库配置（ChestnutCMS集成）
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=5Secsgo100
MYSQL_DATABASE=chestnut_cms

# ChestnutCMS配置
CHESTNUT_CMS_BASE_URL=https://www.gzmdrw.cn
```

### 4. 启动 Qdrant 数据库

```bash
# 使用 Docker 启动 Qdrant（推荐）
docker run -p 6333:6333 -p 6334:6334 \
    -v $(pwd)/qdrant_storage:/qdrant/storage:z \
    qdrant/qdrant

# 或者下载并运行 Qdrant 二进制文件
# 参考：https://qdrant.tech/documentation/guides/installation/
```

### 5. 启动应用

```bash
# 启动 FastAPI 服务
python -m uvicorn backend.app.main:app --host 0.0.0.0 --port 8000 --reload
```

### 6. 使用方法

1. **文档上传**: 将 TXT 文档放入 `data/` 目录，或通过 Web 界面上传
2. **CMS 同步**: 访问 http://localhost:8000/documents 进行 ChestnutCMS 文章同步
3. **智能问答**: 访问 http://localhost:8000 开始与文档对话
4. **跨域访问**: 可以在外部网站中嵌入 `ai_chat_cors/ai_chat.html`

## 📁 项目结构

```
fast-gzmdrw-chat/
├── backend/                 # 后端代码
│   ├── app/                # FastAPI应用
│   │   ├── __init__.py
│   │   ├── main.py         # 主应用文件
│   │   ├── dependencies.py # 依赖注入
│   │   ├── api/            # API路由层
│   │   │   └── v1/         # API v1版本
│   │   │       ├── api.py             # 路由聚合
│   │   │       ├── query.py           # 查询接口
│   │   │       ├── documents.py       # 文档管理接口
│   │   │       ├── health.py          # 健康检查接口
│   │   │       └── cms_article_sync.py # CMS文章同步接口
│   │   ├── core/           # 核心配置
│   │   │   ├── events.py   # 应用事件
│   │   │   ├── middleware.py # 中间件
│   │   │   └── exceptions.py # 异常处理
│   │   ├── models/         # 数据模型
│   │   │   ├── requests.py # 请求模型
│   │   │   ├── responses.py # 响应模型
│   │   │   └── domain.py   # 领域模型
│   │   ├── services/       # 业务逻辑层 ⭐
│   │   │   ├── __init__.py
│   │   │   ├── rag_service.py        # RAG服务（Qdrant+LlamaIndex）
│   │   │   ├── mysql_service.py      # MySQL数据服务
│   │   │   └── chestnut_cms_service.py # CMS同步协调服务
│   │   └── utils/          # 工具函数
│   ├── config/             # 配置管理
│   │   ├── __init__.py
│   │   ├── settings.py     # 配置类
│   │   ├── logging_config.py # 日志配置
│   │   └── rag_optimization.py # RAG优化配置
│   └── __init__.py
├── frontend/               # 前端代码
│   ├── static/            # 静态资源
│   │   ├── css/
│   │   │   └── style.css  # 样式文件
│   │   └── js/
│   │       ├── app.js     # 主应用逻辑
│   │       └── documents.js # 文档管理逻辑
│   └── templates/
│       ├── index.html     # 聊天页面
│       └── documents.html # 文档管理页面
├── scripts/               # 诊断和维护脚本
│   ├── README.md         # 脚本使用指南
│   ├── check_database.py # 数据库检查
│   ├── rebuild_database.py # 数据库重建
│   ├── fast_reset_database.py # 快速重置数据库
│   ├── diagnose_hnsw_error.py # HNSW错误诊断
│   ├── check_embedding_dimensions.py # 向量维度检查
│   ├── detailed_metadata_check.py # 元数据详细检查
│   └── test_document_management.py # 文档功能测试
├── ai_chat_cors/          # 跨域聊天页面
│   ├── ai_chat.html      # 独立的聊天页面（支持跨域）
│   └── ai_chat_使用说明.md # 跨域使用说明
├── docs/                  # 项目文档
│   ├── prd.md            # 产品需求文档
│   ├── api-reference.md  # API参考文档
│   ├── qdrant-data-dictionary.md # Qdrant数据字典
│   ├── chromadb-storage-analysis.md # 存储架构分析
│   ├── 项目经验.md        # 项目开发经验总结
│   └── CORS实现指南.md    # CORS配置指南
├── data/                  # 文档目录（放置TXT文件）
│   └── sample_document.txt # 示例文档
├── qdrant_storage/        # Qdrant数据存储目录（Docker挂载）
├── .env                  # 环境配置
├── .env.example          # 环境配置示例
├── requirements.txt      # 依赖包
├── test_mysql_connection.py # MySQL连接测试
└── README.md            # 项目说明
```

## 🔧 API 接口

### 页面路由

- `GET /` - 聊天页面（返回 HTML）
- `GET /documents` - 文档管理页面（返回 HTML）

### 系统状态接口

- `GET /api/v1/status` - 获取系统状态

### 文档管理接口

- `POST /api/v1/documents/load` - 重新加载文档
- `GET /api/v1/documents/list` - 获取文档列表
- `POST /api/v1/documents/upload` - 上传单个文档
- `DELETE /api/v1/documents/{filename}` - 删除指定文档

### 查询问答接口

- `POST /api/v1/query` - 查询问答

### CMS 文章同步接口

- `POST /api/v1/sync/chestnut-cms` - 同步 ChestnutCMS 文章
- `GET /api/v1/sync/status` - 获取同步状态

详细的 API 文档请参考 [API 参考文档](docs/api-reference.md)。

## 🛠️ 技术栈

- **后端**: FastAPI + LlamaIndex + Qdrant
- **向量数据库**: Qdrant（高性能向量数据库）
- **外部数据源**: MySQL（ChestnutCMS 集成）
- **前端**: HTML + TailwindCSS + Vanilla JavaScript
- **LLM**: GPT-4o-mini
- **嵌入模型**: text-embedding-3-small（1536 维密集向量）
- **稀疏向量**: Qdrant/bm25（BM25 关键词检索）
- **数据库连接**: PyMySQL + cryptography
- **依赖管理**: pip + requirements.txt

## 💾 数据存储设计

### 核心特性

1. **无用户系统**: 单用户使用，无需认证
2. **对话历史前端存储**: 存储在浏览器 localStorage 中
3. **混合向量存储**: 使用 Qdrant 存储密集向量和稀疏向量
4. **文件系统存储**: 原始 TXT 文件直接存储在 data 目录
5. **CMS 集成**: 自动同步 ChestnutCMS 文章到向量数据库

### Qdrant 数据结构

基于 LlamaIndex 和 Qdrant 最佳实践：

#### Collection: documents

- **向量配置**:
  - `text-dense`: 1536 维密集向量（语义搜索）
  - `text-sparse`: 稀疏向量（BM25 关键词搜索）
- **Point 字段**:
  - 文件信息: `file_url`, `file_name`, `file_path`, `file_size`
  - 文档标识: `document_id`, `content_id`, `ref_doc_id`
  - 内容信息: `title`, `source`, `publish_date`
  - LlamaIndex 节点: `_node_content`, `_node_type`

详细字段说明请参考 [Qdrant 数据字典](docs/qdrant-data-dictionary.md)。

#### 文件替换机制

**核心原则**: 文件名唯一性，同名文件完全替换

1. **检测同名文件**: 以文件名作为唯一标识
2. **完全删除旧数据**: 删除 Qdrant 中所有相关 points 和向量
3. **重新处理新文件**: 完整的文本分块、向量化、存储流程

## 🔍 混合检索

应用采用 Qdrant 和 LlamaIndex 的混合检索机制：

1. **密集向量检索**: 使用 text-embedding-3-small 进行语义相似度检索
2. **稀疏向量检索**: 使用 BM25 算法进行关键词匹配检索
3. **混合权重**: 可配置的权重分配（默认各 50%）
4. **结果融合**: 自动融合两种检索结果
5. **智能重排**: 基于相似度分数重新排序

### 检索模式

- `hybrid`: 混合检索（默认）
- `vector_only`: 仅使用密集向量检索
- `bm25_only`: 仅使用稀疏向量检索

## 📝 使用说明

### 文档管理

1. **TXT 文件上传**: 将文件放入 `data/` 目录或通过 Web 界面上传
2. **CMS 文章同步**: 访问文档管理页面，点击"同步 ChestnutCMS 文章"
3. **文档列表**: 查看已上传的文档和同步状态

### 智能问答

1. **提问技巧**: 提问要具体明确，可询问文档中的具体内容
2. **引用链接**: 回答中包含可点击的原文链接
3. **多轮对话**: 支持上下文理解和连续对话

### 跨域访问

1. **独立页面**: 使用 `ai_chat_cors/ai_chat.html` 进行跨域访问
2. **嵌入网站**: 可以在外部网站中嵌入聊天功能
3. **API 调用**: 支持外部系统通过 API 集成

## ❓ 常见问题

**Q: 如何添加新文档？**
A: 将 TXT 文件放入 data 目录，或通过 Web 界面上传，然后点击"重新加载文档"按钮。

**Q: 如何同步 ChestnutCMS 文章？**
A: 访问 http://localhost:8000/documents 页面，点击"同步 ChestnutCMS 文章"按钮。

**Q: 如果上传同名文件会怎样？**
A: 系统会自动删除旧文件的所有相关数据，然后重新处理新文件。

**Q: 支持哪些文档格式？**
A: 目前支持 TXT 格式和 ChestnutCMS 文章（HTML 转文本）。

**Q: 如何修改模型配置？**
A: 编辑 .env 文件中的 OPENAI_MODEL 和 EMBEDDING_MODEL 参数。

**Q: 数据存储在哪里？**
A: 向量数据存储在 Qdrant 数据库中，原始文件存储在 data 目录。

**Q: 如何清空数据库重新开始？**
A: 重启 Qdrant 服务或删除 qdrant_storage 目录，重启应用即可。

**Q: 如何配置 Qdrant 数据库？**
A: 在 .env 文件中配置 QDRANT_HOST 和 QDRANT_PORT，确保 Qdrant 服务正在运行。

**Q: 如何配置 MySQL 数据库连接？**
A: 在 `.env` 文件中配置 MySQL 相关参数，然后运行 `python test_mysql_connection.py` 测试连接。

**Q: MySQL 连接失败怎么办？**
A: 检查以下几点：1) MySQL 服务是否启动；2) 数据库是否存在；3) 用户名密码是否正确；4) 是否安装了 cryptography 包。

**Q: 如何使用跨域聊天功能？**
A: 使用 `ai_chat_cors/ai_chat.html` 文件，可以在任何网站中嵌入或独立使用。

## 🔧 开发和调试

### 开发模式启动

```bash
# 启动 Qdrant 数据库
docker run -p 6333:6333 -p 6334:6334 -v $(pwd)/qdrant_storage:/qdrant/storage:z qdrant/qdrant

# 启动 FastAPI 应用
python -m uvicorn backend.app.main:app --host 0.0.0.0 --port 8000 --reload
```

### 诊断工具

项目提供了完整的诊断脚本集合，用于排查和解决常见问题：

```bash
# 基础数据库检查
python scripts/check_database.py

# 诊断HNSW错误（如查询失败）
python scripts/diagnose_hnsw_error.py

# 检查向量维度配置
python scripts/check_embedding_dimensions.py

# 重建数据库
python scripts/rebuild_database.py

# 快速重置数据库
python scripts/fast_reset_database.py
```

详细使用说明请参考 [诊断脚本指南](scripts/README.md)。

### 常见问题快速修复

**Qdrant 连接失败**：

1. 确认 Qdrant 服务正在运行：`docker ps` 或检查本地服务
2. 检查 .env 文件中的 QDRANT_HOST 和 QDRANT_PORT 配置
3. 测试连接：访问 http://localhost:6333/dashboard

**查询失败或向量维度错误**：

1. 运行 `python scripts/check_embedding_dimensions.py`
2. 如果维度不匹配，运行 `python scripts/rebuild_database.py`
3. 重新加载文档

**MySQL 连接测试**：

1. 运行 `python test_mysql_connection.py` 测试 MySQL 连接
2. 检查 `.env` 文件中的 MySQL 配置
3. 确认 MySQL 服务正在运行
4. 安装必要依赖：`pip install cryptography`

### 查看日志

应用会在控制台输出详细的调试信息，包括：

- 文档加载进度
- 向量化过程
- 检索过程
- API 请求响应
- CMS 同步状态
- 错误信息

### 性能优化

1. **向量配置**: 调整 chunk_size 和 chunk_overlap 参数
2. **检索权重**: 优化 BM25 和向量检索的权重分配
3. **Qdrant 优化**: 配置 GRPC 连接提升性能
4. **缓存策略**: 利用 Qdrant 的内置缓存机制
5. **定期维护**: 运行诊断脚本检查系统健康状态

### 部署建议

1. **生产环境**: 使用独立的 Qdrant 集群
2. **数据备份**: 定期备份 qdrant_storage 目录
3. **监控**: 监控 Qdrant 性能和存储使用情况
4. **安全**: 配置 Qdrant API 密钥和网络访问控制

## 📚 相关文档

- [产品需求文档](docs/prd.md)
- [API 参考文档](docs/api-reference.md)
- [Qdrant 数据字典](docs/qdrant-data-dictionary.md)
- [CORS 实现指南](docs/CORS实现指南.md)
- [项目开发经验](docs/项目经验.md)

## 📄 许可证

本项目采用 MIT 许可证。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 支持

如有问题，请提交 Issue 或联系开发者。
